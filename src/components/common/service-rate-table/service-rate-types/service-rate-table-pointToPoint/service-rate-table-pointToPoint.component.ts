import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import { DisplayCurrencyValue } from '@/helpers/AccountingHelpers/CurrencyHelpers';
import {
    FORM_VALIDATION_FAILED_MESSAGE,
    GENERIC_ERROR_MESSAGE,
    showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { getServiceTypeById } from '@/helpers/StaticDataHelpers/StaticDataHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { ClientCommonAddress } from '@/interface-models/Client/ClientCommonAddress';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import rateMultipliers, {
    RateMultipliers,
} from '@/interface-models/Generic/ServiceTypes/RateMultipliers';
import { ServiceTypes } from '@/interface-models/Generic/ServiceTypes/ServiceTypes';
import ShortLongName from '@/interface-models/Generic/ShortLongName/ShortLongName';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { VForm } from '@/interface-models/Generic/VForm/VForm';
import { Validation } from '@/interface-models/Generic/Validation';
import ClientFuelSurchargeRate from '@/interface-models/ServiceRates/Client/AdditionalCharges/FuelSurcharge/ClientFuelSurchargeRate';
import ClientServiceRate from '@/interface-models/ServiceRates/Client/ClientServiceRate/ClientServiceRate';
import applicableDemurrages from '@/interface-models/ServiceRates/Demurrage/applicableDemurrages';
import FleetAssetFuelSurchargeRate from '@/interface-models/ServiceRates/FleetAssetOwner/AdditionalCharges/FuelSurcharge/FleetAssetFuelSurchargeRate';
import FleetAssetServiceRate from '@/interface-models/ServiceRates/FleetAssetOwner/FleetAssetServiceRate/FleetAssetServiceRate';
import applicableFuelSurcharges from '@/interface-models/ServiceRates/FuelSurcharges/applicableFuelSurcharges';
import RateTableItems from '@/interface-models/ServiceRates/RateTableItems';
import PointToPointRateType from '@/interface-models/ServiceRates/ServiceTypes/PointToPointServiceRate/PointToPointRateType';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import moment from 'moment-timezone';
import { Component, Prop, Vue } from 'vue-property-decorator';
interface PointToPointTableData {
  index: number;
  serviceTypeName: string;
  serviceTypeId: number;
  fromAddress: string;
  toAddress: string;
  rate: string;
  demurrageGrace: string;
  demurrageRate: string;
  appliedFuelSurcharge: string;
  demurrageFuelSurchargeApplies: string;
  percentage: string;
  isClient: boolean;
}

@Component({
  components: { ConfirmationDialog },
})
export default class ServiceRateTablePointToPoint extends Vue {
  @Prop() public serviceRate: ClientServiceRate | FleetAssetServiceRate;
  @Prop() public fuelSurchargeRate:
    | ClientFuelSurchargeRate
    | FleetAssetFuelSurchargeRate;
  @Prop() public commonAddressList: ClientCommonAddress[];
  @Prop({ default: false }) public isFleetAsset: boolean;
  @Prop({ default: true }) public isEdited: boolean;
  @Prop({ default: '' }) public searchTerm: string;
  public rateId: number = 4;
  // public serviceTypes: ServiceTypes[] = serviceTypes;
  public selectedBulkChanges: number[] = [];
  public bulkChange: number = -1;
  public pointToPointRate: PointToPointRateType | null = null;
  public isNewRate: boolean = false;
  public editedServiceTypeId: number = -1;
  public editedPointToPointIndex: number = -1;
  public selectedServiceTypeId: number = -1;
  public applicableDemurrages: ShortLongName[] = applicableDemurrages.filter(
    (x: ShortLongName) => {
      if (x.id !== 4 && x.id !== 2) {
        return true;
      }

      if (!this.isFleetAsset && x.id === 4) {
        return false;
      }

      if (this.isFleetAsset && x.id === 2) {
        return false;
      }

      return true;
    },
  );
  public applicableFuelSurcharges: ShortLongName[] =
    applicableFuelSurcharges.filter((x: ShortLongName) =>
      !this.isFleetAsset && x.id === 2 ? false : true,
    );
  public displayCurrencyValue: any = DisplayCurrencyValue;
  public $refs!: {
    p2pForm: VForm;
  };

  get dialogIsOpen(): boolean {
    return this.pointToPointRate !== null;
  }

  set dialogIsOpen(value: boolean) {
    if (!value) {
      this.pointToPointRate = null;
    }
  }

  public editPointToPoint(serviceTypeId: number, index: number) {
    this.editedServiceTypeId = serviceTypeId;
    this.editedPointToPointIndex = index;
    const p2pByService = this.pointToPointRateItems.find(
      (x: RateTableItems) => x.serviceTypeId === this.editedServiceTypeId,
    );
    if (!p2pByService) {
      return;
    }
    this.pointToPointRate = JSON.parse(
      JSON.stringify(
        (p2pByService.rateTypeObject as PointToPointRateType[])[
          this.editedPointToPointIndex
        ],
      ),
    );
  }

  get graceTimeInMinutes(): number {
    if (!this.pointToPointRate) {
      return 0;
    }
    return moment
      .duration(this.pointToPointRate.demurrage.graceTimeInMilliseconds)
      .asMinutes();
  }

  set graceTimeInMinutes(value: number) {
    if (!this.pointToPointRate) {
      return;
    }
    this.pointToPointRate.demurrage.graceTimeInMilliseconds = moment
      .duration(value, 'minutes')
      .asMilliseconds();
  }

  public savePointToPointRate() {
    if (!this.$refs.p2pForm.validate()) {
      showNotification(FORM_VALIDATION_FAILED_MESSAGE);
      return;
    }
    if (
      ((this.editedServiceTypeId === -1 ||
        this.editedPointToPointIndex === -1) &&
        !this.isNewRate) ||
      !this.pointToPointRate
    ) {
      showNotification(GENERIC_ERROR_MESSAGE);
      return;
    }
    // when selectedServiceTypeId === -1 it means we need to update an existing point to point rate else we push in a the new point to point rate
    const serviceTypeId = !this.isNewRate
      ? this.editedServiceTypeId
      : this.selectedServiceTypeId;
    const p2pByService: RateTableItems | undefined =
      this.serviceRate.rateTableItems.find(
        (x: RateTableItems) =>
          x.serviceTypeId === serviceTypeId && x.rateTypeId === this.rateId,
      );

    if (!p2pByService) {
      return;
    }

    if (this.selectedServiceTypeId === -1) {
      (p2pByService.rateTypeObject as PointToPointRateType[]).splice(
        this.editedPointToPointIndex,
        1,
        this.pointToPointRate,
      );
    } else {
      (p2pByService.rateTypeObject as PointToPointRateType[]).push(
        this.pointToPointRate,
      );
    }

    showNotification('Please remember to save your changes.', {
      type: HealthLevel.INFO,
    });
    this.cancelPointToPointEdit();
  }

  public generateNewPointToPointRate() {
    this.isNewRate = true;
    this.pointToPointRate = new PointToPointRateType();
    this.selectedServiceTypeId = this.existingServiceTypes[0].serviceTypeId;
  }

  public cancelPointToPointEdit() {
    this.selectedServiceTypeId = -1;
    this.editedServiceTypeId = -1;
    this.editedPointToPointIndex = -1;
    this.isNewRate = false;
    this.pointToPointRate = null;
  }

  get headers(): TableHeader[] {
    let headers: TableHeader[] = [
      {
        text: 'Service',
        align: 'left',
        value: 'serviceTypeName',
        sortable: false,
      },
    ];
    if (!this.isFleetAsset) {
      const clientHeaders: TableHeader[] = [
        {
          text: 'From',
          align: 'left',
          value: 'fromAddress',
          sortable: false,
        },
        {
          text: 'To',
          align: 'left',
          sortable: false,
          value: 'toAddress',
        },
        {
          text: 'Rate',
          align: 'left',
          value: 'rate',
          sortable: false,
        },
      ];

      headers = headers.concat(clientHeaders);
    } else {
      const fleetAssetheaders: TableHeader[] = [
        {
          text: 'Percentage',
          align: 'left',
          value: 'percent',
          sortable: false,
        },
      ];
      headers = headers.concat(fleetAssetheaders);
    }

    const a: TableHeader[] = [
      {
        text: 'Fuel Surcharge',
        align: 'left',
        value: 'appliedFuelSurcharge',
        sortable: false,
      },
      {
        text: 'Demurrage',
        align: 'left',
        value: 'appliedFuelSurcharge',
        sortable: false,
      },
      {
        text: 'Demurrage Rate',
        align: 'left',
        value: 'demurrageRate',
        sortable: false,
      },
    ];

    headers = headers.concat(a);
    if (!this.isFleetAsset) {
      const fleetAssetDemurrageGraceHeader: TableHeader = {
        text: 'Demurrage Grace',
        align: 'left',
        value: 'demurrageGrace',
        sortable: false,
      };
      headers.push(fleetAssetDemurrageGraceHeader);
    }
    const clientDemurrageFuelHeader: TableHeader = {
      text: 'Demurrage Fuel Surcharge',
      align: 'left',
      value: 'demurrageFuelSurchargeApplies',
      sortable: false,
    };

    headers.push(clientDemurrageFuelHeader);
    return headers;
  }

  get pointToPointRateItems(): RateTableItems[] {
    let items = this.serviceRate.rateTableItems.filter(
      (item: RateTableItems) => item.rateTypeId === this.rateId,
    );

    // Apply search filter if searchTerm is provided
    if (this.searchTerm && this.searchTerm.trim().length > 0) {
      const searchTerm = this.searchTerm.toLowerCase().trim();
      items = items.filter((item) => {
        const shortName = item.serviceShortName?.toLowerCase() || '';
        const longName = item.serviceLongName?.toLowerCase() || '';
        return shortName.includes(searchTerm) || longName.includes(searchTerm);
      });
    }

    return items;
  }

  get existingServiceTypes() {
    const existingServiceTypeIds = this.pointToPointRateItems.map(
      (x: RateTableItems) => x.serviceTypeId,
    );
    return useCompanyDetailsStore().getServiceTypesList.filter(
      (service: ServiceTypes) =>
        existingServiceTypeIds.includes(service.serviceTypeId),
    );
  }

  get pointToPointRateTableItems(): PointToPointTableData[] {
    const tableData: PointToPointTableData[] = [];
    for (const service of this.pointToPointRateItems) {
      if (!service.serviceTypeId) {
        continue;
      }
      const serviceTypeName = this.serviceTypeName(service.serviceTypeId);
      const p2pRates: PointToPointRateType[] =
        service.rateTypeObject as PointToPointRateType[];
      const p2pTableRate: PointToPointTableData[] = p2pRates.map(
        (p2p: PointToPointRateType, index: number) => {
          const appliedFuelSurcharge = applicableFuelSurcharges.find(
            (x: ShortLongName) => p2p.appliedFuelSurchargeId === x.id,
          );
          const multiplier = rateMultipliers.find(
            (rateMultiplier: RateMultipliers) =>
              rateMultiplier.id === p2p.demurrage.incrementMultiplier,
          );

          const demurrageGraceAsMinutes = moment
            .duration(p2p.demurrage.graceTimeInMilliseconds)
            .asMinutes();

          const appliedDemurrage = this.applicableDemurrages.find(
            (x: ShortLongName) => x.id === p2p.demurrage.appliedDemurrageId,
          );
          return {
            index,
            serviceTypeName,
            serviceTypeId: service.serviceTypeId ? service.serviceTypeId : -1,
            fromAddress: !this.isFleetAsset
              ? this.commonAddressLocation(p2p.fromAddressReference)
              : '',
            toAddress: !this.isFleetAsset
              ? this.commonAddressLocation(p2p.toAddressReference)
              : '',
            rate: '$' + this.displayCurrencyValue(p2p.rate),
            appliedFuelSurcharge: appliedFuelSurcharge
              ? appliedFuelSurcharge.shortName
              : '-',
            appliedDemurrageCharge: appliedDemurrage
              ? appliedDemurrage.shortName
              : '-',
            demurrageGrace: !demurrageGraceAsMinutes
              ? 'None'
              : moment.duration(demurrageGraceAsMinutes, 'minutes').humanize(),
            demurrageRate:
              '$' +
              this.displayCurrencyValue(p2p.demurrage.rate) +
              ' per ' +
              p2p.demurrage.increment +
              ' ' +
              (multiplier ? multiplier.longName : '-'),
            percentage: p2p.percentage + '%',
            isClient: !this.isFleetAsset,
            demurrageFuelSurchargeApplies: p2p.demurrage
              .demurrageFuelSurchargeApplies
              ? 'Apply'
              : `Don't Apply`,
          };
        },
      );
      tableData.push(...p2pTableRate);
    }

    return tableData;
  }

  public serviceTypeName(serviceTypeId: number): string {
    const serviceType = getServiceTypeById(serviceTypeId);
    return serviceType ? serviceType.optionSelectName : '-';
  }

  public commonAddressLocation(addressId: string): string {
    const commonAddress = this.commonAddressList.find(
      (x: ClientCommonAddress) => x._id === addressId,
    );
    return commonAddress ? commonAddress.address.formattedAddress : '-';
  }

  public removeRate(): void {
    const p2pByService: RateTableItems | undefined =
      this.serviceRate.rateTableItems.find(
        (x: RateTableItems) =>
          x.serviceTypeId === this.editedServiceTypeId &&
          x.rateTypeId === this.rateId,
      );
    const serviceTypeName = this.serviceTypeName(this.editedServiceTypeId);
    if (!p2pByService) {
      return;
    }
    (p2pByService.rateTypeObject as PointToPointRateType[]).splice(
      this.editedPointToPointIndex,
      1,
    );

    this.pointToPointRate = null;

    showNotification(
      serviceTypeName +
        ' zone rate successfully removed. Please Remember to save.',
      { type: HealthLevel.INFO },
    );
    this.cancelPointToPointEdit();
  }

  get validate(): Validation {
    return validationRules;
  }
}
