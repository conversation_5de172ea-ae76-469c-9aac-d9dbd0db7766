.zone {
  .service-name {
    color: var(--bg-light);
    font-weight: 600;
  }

  .zone-child-row {
    border-top: none !important;
    margin-bottom: 1px !important;

    .small-input {
      border-right: 1px solid $border-color;
    }

    .zone-child-text {
      padding-left: 12px;
    }
  }
}
.extra-wide {
  width: 150px !important;
}

.fixed-width {
  width: 120px !important;
}

.break-word {
  word-break: break-all;
  overflow-wrap: break-word;
}

.copy-controls {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-right: 12px;
}

.radio-group {
  display: flex;
  justify-content: center;
}

.v-input--checkbox {
  display: flex;
  justify-content: center;
}

.bulk-selection-container {
  position: absolute;
  right: -55px;
  top: -88px;
  width: 204px;
  display: flex;
  justify-content: center;
}

.bulkChangeInactive {
  display: none;
}

.alert-text {
  text-transform: uppercase;
  font-weight: 500;
}

.form-container {
  background-color: var(--background-color-400);
  border-bottom: 1px solid $translucent;

  &.item-selected {
    background-color: var(--background-color-300) !important;
    border-bottom: 0.5px solid $info;
  }

  &.empty-rate-item {
    background-color: rgba(255, 193, 7, 0.1) !important;
    border-left: 1px solid $warning !important;
  }
}
