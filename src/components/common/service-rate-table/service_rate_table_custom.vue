<template>
  <section class="service-rate-table-custom pa-3 pt-0 mb-4">
    <v-layout>
      <v-flex
        md12
        v-if="
          (serviceRateType === 'FLEET_ASSET' &&
            fleetAssetServiceRate &&
            fleetAssetServiceRate.rateTableItems.length > 0) ||
          (serviceRateType === 'CLIENT' &&
            clientServiceRate &&
            clientServiceRate.rateTableItems.length > 0)
        "
      >
        <!-- Search Input Section -->
        <v-flex class="search-section mb-3">
          <v-text-field
            v-model="searchTerm"
            append-icon="search"
            label="Search Service Types"
            placeholder="Search by service type name..."
            solo
            flat
            class="v-solo-custom"
            dense
            hide-details
            single-line
            clearable
          />
        </v-flex>

        <v-flex class="button-group">
          <v-tooltip
            v-for="rateType in rateTypesList"
            :key="rateType.rateTypeId"
            bottom
            :disabled="true"
          >
            <template v-slot:activator="{ on }">
              <span v-on="on">
                <v-btn
                  :class="{
                    'v-btn--active':
                      selectedRateController === rateType.rateTypeId,
                  }"
                  flat
                  @click="showSelectedRate(rateType)"
                >
                  <span class="px-2">
                    <strong>
                      {{
                        rateType.shortName === 'P2P'
                          ? rateType.shortName
                          : rateType.longName
                      }}
                    </strong>
                    Rates
                  </span>
                </v-btn>
              </span>
            </template>
            <!-- Tooltip text goes here, but tooltips are disabled for now -->
            Tooltip disabled
          </v-tooltip>
        </v-flex>

        <v-layout>
          <v-flex md12>
            <v-form ref="form" v-if="serviceRateType === 'FLEET_ASSET'">
              <ServiceRateTableTime
                :serviceRate="fleetAssetServiceRate"
                v-if="selectedRateController === JobRateType.TIME"
                :client="false"
                :isEdited="isEdited"
                :isFleetAsset="true"
                :adminPage="false"
                :fixedTopPanel="false"
                :searchTerm="searchTerm"
              />
              <ServiceRateTableZone
                :serviceRate="fleetAssetServiceRate"
                :isFleetAsset="true"
                :fuelSurchargeRate="fuelSurchargeRate"
                v-if="selectedRateController === JobRateType.ZONE"
                :isEdited="isEdited"
                :searchTerm="searchTerm"
              />

              <ServiceRateTableDistance
                :type="serviceRateType"
                :serviceRate="fleetAssetServiceRate"
                v-if="selectedRateController === JobRateType.DISTANCE"
                :isEdited="isEdited"
                :searchTerm="searchTerm"
              />
              <ServiceRateTablePointToPoint
                :isFleetAsset="true"
                :serviceRate="fleetAssetServiceRate"
                :fuelSurchargeRate="fuelSurchargeRate"
                v-if="selectedRateController === JobRateType.POINT_TO_POINT"
                :isEdited="isEdited"
                :searchTerm="searchTerm"
              />
              <ServiceRateTableUnit
                :serviceRate="fleetAssetServiceRate"
                :fuelSurchargeRate="fuelSurchargeRate"
                :isEdited="isEdited"
                v-if="selectedRateController === JobRateType.UNIT"
                :isClient="false"
                :searchTerm="searchTerm"
              />
            </v-form>
            <v-form ref="form" v-if="serviceRateType === 'CLIENT'">
              <ServiceRateTableTime
                :serviceRate="clientServiceRate"
                :isNew="isNewServiceRates"
                :isEdited="isEdited"
                v-if="selectedRateController === JobRateType.TIME"
                :isClient="true"
                :isFleetAsset="false"
                :adminPage="false"
                :fixedTopPanel="false"
                :searchTerm="searchTerm"
              />
              <ServiceRateTableZone
                :serviceRate="clientServiceRate"
                v-if="selectedRateController === JobRateType.ZONE"
                :isEdited="isEdited"
                :fuelSurchargeRate="fuelSurchargeRate"
                :searchTerm="searchTerm"
              />
              <ServiceRateTableDistance
                :type="serviceRateType"
                :serviceRate="clientServiceRate"
                v-if="selectedRateController === JobRateType.DISTANCE"
                :isEdited="isEdited"
                :searchTerm="searchTerm"
              />
              <ServiceRateTablePointToPoint
                v-if="selectedRateController === JobRateType.POINT_TO_POINT"
                :serviceRate="clientServiceRate"
                :isEdited="isEdited"
                :fuelSurchargeRate="fuelSurchargeRate"
                :commonAddressList="commonAddressList"
                :searchTerm="searchTerm"
              />
              <ServiceRateTableUnit
                :serviceRate="clientServiceRate"
                v-if="selectedRateController === JobRateType.UNIT"
                :isEdited="isEdited"
                :fuelSurchargeRate="fuelSurchargeRate"
                :isClient="true"
                :searchTerm="searchTerm"
              />
            </v-form>
          </v-flex>
        </v-layout>
      </v-flex>

      <v-flex md12 v-else>
        <v-sheet class="info-sheet" v-if="rateTableForEntityType()">
          <p class="title">Service Rate information</p>
          <p>Name: {{ rateTableForEntityType()?.name }}</p>
          <p>
            Outside Metro Rate:
            {{ rateTableForEntityType()?.outsideMetroRate }}%
          </p>

          <p>
            Valid from:
            {{
              returnFormattedDate(rateTableForEntityType()?.validFromDate ?? 0)
            }}
          </p>
          <p>
            Valid to:
            {{
              returnFormattedDate(rateTableForEntityType()?.validToDate ?? 0)
            }}
          </p>
          <p class="mb-0">Custom rates: No Custom rates where selected.</p>
        </v-sheet>
      </v-flex>
    </v-layout>
  </section>
</template>

<script setup lang="ts">
import ServiceRateTablePointToPoint from '@/components/common/service-rate-table/service-rate-types/service-rate-table-pointToPoint/index.vue';
import ServiceRateTableTime from '@/components/common/service-rate-table/service-rate-types/service-rate-table-time/index.vue';
import ServiceRateTableUnit from '@/components/common/service-rate-table/service-rate-types/service-rate-table-unit/index.vue';
import ServiceRateTableZone from '@/components/common/service-rate-table/service-rate-types/service-rate-table-zone/index.vue';
import ServiceRateTableDistance from '@/components/common/service-rate-table/service-rate-types/service_rate_table_distance/service_rate_table_distance.vue';
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { ClientCommonAddress } from '@/interface-models/Client/ClientCommonAddress';
import {
  JobRateType,
  returnSortedRateTypesList,
  ServiceTypeRates,
} from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import {
  ValidFromRequiredEdit,
  ValidToRequiredEdit,
} from '@/interface-models/Generic/ValidRequiredEdit';
import ClientServiceRate from '@/interface-models/ServiceRates/Client/ClientServiceRate/ClientServiceRate';
import FleetAssetServiceRate from '@/interface-models/ServiceRates/FleetAssetOwner/FleetAssetServiceRate/FleetAssetServiceRate';
import { FuelSurchargeRate } from '@/interface-models/ServiceRates/FuelSurcharge/FuelSurchargeRate';
import { RateEntityType } from '@/interface-models/ServiceRates/RateEntityType';
import RateTableItems from '@/interface-models/ServiceRates/RateTableItems';
import ServiceRateCard from '@/interface-models/ServiceRates/ServiceRateCard';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useServiceRateStore } from '@/store/modules/ServiceRateStore';
import moment from 'moment-timezone';
import {
  computed,
  ComputedRef,
  onMounted,
  ref,
  Ref,
  watchEffect,
  WritableComputedRef,
} from 'vue';

const props = withDefaults(
  defineProps<{
    serviceRateType?: RateEntityType;
    isEdited: boolean;
    isNewServiceRates?: boolean;
    allowEditOnDateRange?: boolean;
    // Fleet Asset
    fleetAssetServiceRate?: FleetAssetServiceRate | null;
    allFleetAssetServiceRatesList?: ServiceRateCard[];
    // Client
    clientServiceRate?: ClientServiceRate | null;
    allClientServiceRatesList?: ServiceRateCard[];
    // Fuel
    fuelSurchargeRate: FuelSurchargeRate;
    // client common address list. used in p2p rate
    commonAddressList?: ClientCommonAddress[];
  }>(),
  {
    serviceRateType: RateEntityType.FLEET_ASSET,
    isEdited: true,
    allFleetAssetServiceRatesList: () => [],
    allClientServiceRatesList: () => [],
    commonAddressList: () => [],
    clientServiceRate: null,
    fleetAssetServiceRate: null,
  },
);

defineExpose({ saveServiceRateData });

const companyDetailsStore = useCompanyDetailsStore();
const serviceRateStore = useServiceRateStore();

const selectedRate: Ref<number> = ref(1);
const form = ref<any>(null);
const searchTerm: Ref<string> = ref('');
const emit = defineEmits(['savedRateTable']);

const rateTypesList: ComputedRef<ServiceTypeRates[]> = computed(() => {
  const rateTableItems = rateTableItemsForEntityType() ?? [];
  const presentRateTypeIds = new Set(rateTableItems.map((r) => r.rateTypeId));
  return returnSortedRateTypesList(
    companyDetailsStore.divisionCustomConfig?.operations?.rateTypeDisplayOrder,
  )
    .filter((s) => !s.adhoc && s.rateTypeId !== JobRateType.ZONE_TO_ZONE)
    .filter((s) => presentRateTypeIds.has(s.rateTypeId));
});

const selectedRateController: WritableComputedRef<number> = computed({
  get(): number {
    const rateTableItems: RateTableItems[] | null =
      rateTableItemsForEntityType(); // Ensure this is reactive
    if (!rateTableItems || rateTableItems.length === 0) {
      return 1; // Return default instead of mutating selectedRate
    }

    const rateIds = [...new Set(rateTableItems.map((r) => r.rateTypeId))];
    if (rateIds.length === 0) {
      return 1;
    }
    if (!rateIds.includes(selectedRate.value)) {
      return rateIds[0]; // Return a valid rate instead of modifying state
    }

    return selectedRate.value;
  },
  set(value: number): void {
    selectedRate.value = value;
  },
});

// Ensure reactivity by watching changes in rateTableItems
watchEffect(() => {
  const rateTableItems = rateTableItemsForEntityType();
  if (!rateTableItems || rateTableItems.length === 0) {
    selectedRate.value = 1;
  } else {
    const rateIds = [...new Set(rateTableItems.map((r) => r.rateTypeId))];
    if (!rateIds.includes(selectedRate.value)) {
      selectedRate.value = rateIds[0];
    }
  }
});

// // return rate type names that are client and not default
// const selectedClientRateNames: ComputedRef<string[]> = computed(() => {
//   // services have many rate types so remove duplicates of RateTypes in filteredRateTypes array
//   const rateTableItems = rateTableItemsForEntityType() ?? [];
//   const uniqueRateTypes: number[] = [
//     ...new Set(rateTableItems.map((r) => r.rateTypeId)),
//   ]
//     .filter((id) => id !== JobRateType.ZONE_TO_ZONE)
//     .sort((a, b) => a - b);

//   // get rateType long names and push to filteredServiceTypes
//   const filteredServiceRateTypes: string[] = [];
//   for (const id of uniqueRateTypes) {
//     console.log('test');
//     const index = rateTypesList.value.findIndex(
//       (item) => item.rateTypeId === id,
//     );
//     if (rateTypesList.value[index].longName === 'Point to Point') {
//       filteredServiceRateTypes.push(rateTypesList.value[index].shortName);
//     } else {
//       filteredServiceRateTypes.push(rateTypesList.value[index].longName);
//     }
//   }

//   return filteredServiceRateTypes;
// });

// Get the list of RateTableItems for component instance's entity type
function rateTableItemsForEntityType(): RateTableItems[] | null {
  let rateTableItems: RateTableItems[] | null = null;
  // Set rateTableItems based on entity type
  if (
    props.serviceRateType === RateEntityType.CLIENT &&
    props.clientServiceRate
  ) {
    rateTableItems = props.clientServiceRate.rateTableItems;
  } else if (
    props.serviceRateType === RateEntityType.FLEET_ASSET &&
    props.fleetAssetServiceRate
  ) {
    rateTableItems = props.fleetAssetServiceRate.rateTableItems;
  }
  return rateTableItems;
}

// Get the relevant Service Rate Table for component instance's entity type
function rateTableForEntityType(): ServiceRateCard | null {
  let serviceRate: ServiceRateCard | null = null;
  if (
    props.serviceRateType === RateEntityType.CLIENT &&
    props.clientServiceRate
  ) {
    serviceRate = props.clientServiceRate;
  } else if (
    props.serviceRateType === RateEntityType.FLEET_ASSET &&
    props.fleetAssetServiceRate
  ) {
    serviceRate = props.fleetAssetServiceRate;
  }
  return serviceRate;
}

// Get the relevant Service Rate Table for component instance's entity type
function rateTableListForEntityType(): ServiceRateCard[] | null {
  let serviceRateList: ServiceRateCard[] | null = null;
  if (
    props.serviceRateType === RateEntityType.CLIENT &&
    props.allClientServiceRatesList
  ) {
    serviceRateList = props.allClientServiceRatesList;
  } else if (
    props.serviceRateType === RateEntityType.FLEET_ASSET &&
    props.fleetAssetServiceRate
  ) {
    serviceRateList = props.allFleetAssetServiceRatesList;
  }
  return serviceRateList;
}

// show correct component based on selected rate
function showSelectedRate(type: ServiceTypeRates): void {
  selectedRateController.value = type.rateTypeId;
}

// show rate type component on initial load
function activateRate(): void {
  if (rateTypesList.value.length > 0) {
    selectedRateController.value =
      companyDetailsStore.divisionCustomConfig?.operations?.defaultRateTypeId ??
      rateTypesList.value[0].rateTypeId;
  }
}

function showValidationErrorMessage() {
  showNotification(FORM_VALIDATION_FAILED_MESSAGE);
}
// Save service rate table, and send off requests for any other edits to
// summary information that may need to be made
async function saveServiceRateData(): Promise<void> {
  if (!form.value) {
    showValidationErrorMessage();
    return;
  }
  const serviceRateTable = rateTableForEntityType();
  if (!serviceRateTable) {
    showNotification('Something went wrong. Please try again.');
    return;
  }

  const validation = form.value.validate();

  const dateStartValid = typeof serviceRateTable.validFromDate === 'number';

  const dateEndValid = typeof serviceRateTable.validToDate === 'number';
  const elementError = document.getElementsByClassName('error-text');
  const validationError =
    !validation || !dateStartValid || !dateEndValid || elementError.length > 0;
  if (validationError) {
    showValidationErrorMessage();
    return;
  }

  // Check if there were any changes to validTo dates in other adjacent
  // service rates and dispatch request accordingly
  if (validFromEdits.value.edit) {
    const rateListToSearch = rateTableListForEntityType();
    if (rateListToSearch) {
      const foundRate = rateListToSearch.find(
        (x: ServiceRateCard) => x.tableId === validFromEdits.value.tableId,
      );
      if (foundRate !== undefined) {
        foundRate.validToDate = validFromEdits.value.validToValue;
        if (props.serviceRateType === RateEntityType.CLIENT) {
          serviceRateStore.updateClientServiceRateSummary(
            foundRate as ClientServiceRate,
          );
        } else {
          serviceRateStore.updateFleetAssetServiceRateSummary(
            foundRate as FleetAssetServiceRate,
          );
        }
      }
    }
  }
  // Check if there were any changes to validFrom dates in other adjacent
  // service rates and dispatch request accordingly
  if (validToEdits.value.edit) {
    const rateListToSearch = rateTableListForEntityType();
    if (rateListToSearch) {
      const foundRate = rateListToSearch.find(
        (x: ServiceRateCard) => x.tableId === validToEdits.value.tableId,
      );
      if (foundRate !== undefined) {
        foundRate.validFromDate = validToEdits.value.validFromValue;
        if (props.serviceRateType === RateEntityType.CLIENT) {
          serviceRateStore.updateClientServiceRateSummary(
            foundRate as ClientServiceRate,
          );
        } else {
          serviceRateStore.updateFleetAssetServiceRateSummary(
            foundRate as FleetAssetServiceRate,
          );
        }
      }
    }
  }
  saveServiceRateTables();
}

/**
 * Dispatches request to save the client or fleet asset rate tables.
 */
async function saveServiceRateTables() {
  let result: boolean;

  if (
    props.serviceRateType === RateEntityType.CLIENT &&
    props.clientServiceRate
  ) {
    result = await serviceRateStore.saveClientServiceRates(
      props.clientServiceRate as ClientServiceRate,
    );
  } else if (
    props.serviceRateType === RateEntityType.FLEET_ASSET &&
    props.fleetAssetServiceRate
  ) {
    result = await serviceRateStore.saveFleetAssetServiceRate(
      props.fleetAssetServiceRate as FleetAssetServiceRate,
    );
  }

  if (result) {
    emit('savedRateTable');
  } else {
    showNotification('Something went wrong. Please try again.', {
      title: 'Save Rate Card Failed',
    });
  }
}

const validFromEdits: ComputedRef<ValidFromRequiredEdit> = computed(() => {
  const requiredEdits: ValidFromRequiredEdit = {
    edit: false,
    tableId: -1,
    validToValue: 0,
  };
  // Use the appropriate serviceRate and all list depending on the Entity Type
  // we're dealing with
  const serviceRateTable = rateTableForEntityType();
  const rateListToSearch = rateTableListForEntityType();
  if (!serviceRateTable || !rateListToSearch) {
    return requiredEdits;
  }

  let foundEditedServiceRate = rateListToSearch.find(
    (rate: ServiceRateCard) => rate.tableId === serviceRateTable.tableId,
  );
  if (props.isNewServiceRates) {
    foundEditedServiceRate = serviceRateTable;
  }
  if (
    serviceRateTable.validFromDate !== null &&
    previousServiceRate.value !== undefined &&
    previousServiceRate.value.validToDate !== null &&
    foundEditedServiceRate !== undefined &&
    foundEditedServiceRate.validFromDate !== null
  ) {
    if (
      serviceRateTable.validFromDate <= previousServiceRate.value.validToDate ||
      serviceRateTable.validFromDate > foundEditedServiceRate.validFromDate
    ) {
      requiredEdits.edit = true;
      requiredEdits.tableId = previousServiceRate.value.tableId;
      requiredEdits.validToValue = moment(serviceRateTable.validFromDate)
        .tz(companyDetailsStore.userLocale)
        .subtract(1, 'day')
        .endOf('day')
        .valueOf();
    }
  }
  return requiredEdits;
});

const previousServiceRate: ComputedRef<ServiceRateCard | undefined> = computed(
  () => {
    // Use the appropriate serviceRate and all list depending on the Entity Type we're dealing with
    const serviceRateTable = rateTableForEntityType();
    const rateListToSearch = rateTableListForEntityType();
    if (!serviceRateTable || !rateListToSearch) {
      return;
    }
    const foundEditedServiceRate = rateListToSearch.find(
      (rate: ServiceRateCard) => rate.tableId === serviceRateTable.tableId,
    );
    if (foundEditedServiceRate !== undefined) {
      const serviceRate = rateListToSearch.find(
        (x: ServiceRateCard) =>
          x.validToDate! < foundEditedServiceRate!.validFromDate! &&
          x.validToDate! > foundEditedServiceRate!.validFromDate! - 80400000,
      );
      if (serviceRate !== undefined) {
        return serviceRate;
      }
    }
    return undefined;
  },
);

const validToEdits: ComputedRef<ValidToRequiredEdit> = computed(() => {
  const requiredEdits: ValidToRequiredEdit = {
    edit: false,
    tableId: -1,
    validFromValue: 0,
  };
  // Use the appropriate serviceRate and all list depending on the Entity Type we're dealing with
  const serviceRateTable = rateTableForEntityType();
  const rateListToSearch = rateTableListForEntityType();
  if (!serviceRateTable || !rateListToSearch) {
    return requiredEdits;
  }
  let foundEditedServiceRate = rateListToSearch.find(
    (rate: ServiceRateCard) => rate.tableId === serviceRateTable.tableId,
  );
  if (props.isNewServiceRates) {
    foundEditedServiceRate = serviceRateTable;
  }

  if (
    serviceRateTable.validToDate !== null &&
    nextServiceRate.value !== undefined &&
    nextServiceRate.value.validFromDate !== null &&
    foundEditedServiceRate !== undefined &&
    foundEditedServiceRate.validToDate !== null
  ) {
    if (
      serviceRateTable.validToDate >= nextServiceRate.value.validFromDate ||
      serviceRateTable.validToDate < foundEditedServiceRate.validToDate
    ) {
      requiredEdits.edit = true;
      requiredEdits.tableId = nextServiceRate.value.tableId;
      requiredEdits.validFromValue = moment(serviceRateTable.validToDate)
        .tz(companyDetailsStore.userLocale)
        .add(1, 'day')
        .startOf('day')
        .valueOf();
    }
  }
  return requiredEdits;
});

const nextServiceRate: ComputedRef<ServiceRateCard | undefined> = computed(
  () => {
    const serviceRateTable = rateTableForEntityType();
    const rateListToSearch = rateTableListForEntityType();
    if (!serviceRateTable || !rateListToSearch) {
      return;
    }
    const foundEditedServiceRate = rateListToSearch.find(
      (rate: ServiceRateCard) => rate.tableId === serviceRateTable.tableId,
    );
    if (foundEditedServiceRate !== undefined) {
      const serviceRate = props.allFleetAssetServiceRatesList.find(
        (x: ServiceRateCard) =>
          x.validFromDate! > foundEditedServiceRate.validToDate! &&
          x.validFromDate! < foundEditedServiceRate.validToDate! + 80400000,
      );
      return serviceRate;
    }
    return undefined;
  },
);

onMounted(() => {
  window.scrollTo(0, 0);
  activateRate();
});
</script>

<style lang="scss" scoped>
.top-panel {
  position: fixed;
  transition: 0.2s;
  background-color: $secondary;
  top: 39px;
  z-index: 199;
  height: 42px;
  left: calc(25% + 37px);
  width: calc(75% - 37px);
}

.tab-rates-container {
  position: fixed;
  top: 39px;
  left: 50%;
  z-index: 200;
}

.search-section {
  .v-text-field {
    max-width: 400px;
  }
}

.info-sheet {
  padding: 18px;
  background-color: var(--background-color-400) !important;
  border: 1px solid var(--border-color);
  border-radius: 12px;

  p {
    color: var(--text-color);
  }

  .title {
    color: var(--bg-light-blue);
  }
}
</style>
